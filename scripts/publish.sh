#!/bin/bash
set -e

if [ -z "$1" ]; then
    echo "Usage: $0 <package-name>"
    exit 1
fi

PACKAGE_NAME=$1
PACKAGE_DIR="packages/$PACKAGE_NAME"

if [ ! -d "$PACKAGE_DIR" ]; then
    echo "Error: Package directory '$PACKAGE_DIR' not found"
    exit 1
fi

echo "Publishing $PACKAGE_NAME..."
cd "$PACKAGE_DIR"

# Check if environment is initialized
if [ ! -f ".venv/bin/activate" ]; then
    echo "Error: Package environment not initialized. Run: ./scripts/init-package-env.sh $PACKAGE_NAME"
    exit 1
fi

# Activate environment
source .venv/bin/activate

# Clean previous builds
rm -rf dist/ build/ *.egg-info

# Build the package
python -m build

# Upload to PyPI
echo "Uploading to PyPI..."
twine upload dist/*

echo "Successfully published $PACKAGE_NAME!"