#!/bin/bash
set -e

# Development helper script for working with individual packages

PACKAGE_NAME=$1
COMMAND=$2

if [ -z "$PACKAGE_NAME" ]; then
    echo "Usage: $0 <package-name> [command]"
    echo ""
    echo "Commands:"
    echo "  test     - Run tests for the package"
    echo "  shell    - Open a shell with the package environment"
    echo "  add      - Add a dependency to the package"
    echo "  install  - Install the package in editable mode"
    echo "  format   - Format the package code"
    echo "  lint     - Lint the package"
    echo "  type     - Type check the package"
    echo "  hooks    - Run pre-commit hooks on the package"
    echo "  init     - Initialize package environment"
    echo "  init-top - Initialize package environment in root directory"
    echo "  sync     - Sync dev dependencies from root"
    echo ""
    echo "Examples:"
    echo "  ./scripts/dev.sh cobo-async-signals test"
    echo "  ./scripts/dev.sh cobo-async-signals add aiofiles"
    echo "  ./scripts/dev.sh cobo-async-signals shell"
    echo "  ./scripts/dev.sh cobo-async-signals init-top"
    echo "  ./scripts/dev.sh cobo-async-signals init"
    exit 1
fi

PACKAGE_DIR="packages/$PACKAGE_NAME"

if [ ! -d "$PACKAGE_DIR" ]; then
    echo "Error: Package '$PACKAGE_NAME' not found in packages/"
    exit 1
fi

# Function to check if environment is initialized
check_env() {
    if [ ! -f "$PACKAGE_DIR/.venv/bin/activate" ]; then
        echo "Error: Package environment not initialized."
        echo "Run: ./scripts/dev.sh $PACKAGE_NAME init"
        exit 1
    fi
}

# Function to run command in package environment
run_in_env() {
    cd "$PACKAGE_DIR"
    source .venv/bin/activate
    "$@"
}

case "$COMMAND" in
    test)
        echo "Running tests for $PACKAGE_NAME..."
        check_env
        run_in_env python -m pytest tests -v
        ;;

    shell)
        echo "Opening shell for $PACKAGE_NAME..."
        check_env
        cd "$PACKAGE_DIR"
        source .venv/bin/activate
        echo "Activated environment for $PACKAGE_NAME"
        echo "Type 'exit' to leave the environment"
        exec $SHELL
        ;;

    add)
        if [ -z "$3" ]; then
            echo "Usage: $0 $PACKAGE_NAME add <dependency> [--dev]"
            exit 1
        fi

        check_env
        cd "$PACKAGE_DIR"
        source .venv/bin/activate

        if [ "$4" == "--dev" ]; then
            echo "Adding $3 as dev dependency to $PACKAGE_NAME..."
            pip install "$3"
            echo "Note: Add '$3' to pyproject.toml [project.optional-dependencies.dev] to persist"
        else
            echo "Adding $3 as dependency to $PACKAGE_NAME..."
            pip install "$3"
            echo "Note: Add '$3' to pyproject.toml [project.dependencies] to persist"
        fi
        ;;

    install)
        echo "Installing $PACKAGE_NAME in editable mode..."
        check_env
        cd "$PACKAGE_DIR"
        unset VIRTUAL_ENV
        uv sync --all-extras
        ;;

    format)
        echo "Formatting $PACKAGE_NAME..."
        check_env
        run_in_env black .
        run_in_env isort .
        ;;

    lint)
        echo "Linting $PACKAGE_NAME..."
        check_env
        run_in_env ruff check .
        ;;

    type)
        echo "Type checking $PACKAGE_NAME..."
        check_env
        run_in_env mypy src
        ;;

    hooks)
        echo "Running pre-commit hooks on $PACKAGE_NAME..."
        check_env
        run_in_env pre-commit run --all-files --config ../../.pre-commit-config.yaml
        ;;

    init)
        echo "Initializing environment for $PACKAGE_NAME..."
        ./scripts/init-package-env.sh "$PACKAGE_NAME"
        ;;

    init-top)
        echo "Initializing top-level environment for $PACKAGE_NAME..."
        ./scripts/init-top-env.sh "$PACKAGE_NAME"
        ;;

    sync)
        echo "Syncing dev dependencies for $PACKAGE_NAME..."
        ./scripts/sync-dev-deps.sh
        echo "Dependencies synced. Reinstalling $PACKAGE_NAME..."
        check_env
        cd "$PACKAGE_DIR"
        unset VIRTUAL_ENV
        uv sync --all-extras
        ;;

    *)
        echo "Unknown command: $COMMAND"
        echo "Run '$0' without arguments to see usage"
        exit 1
        ;;
esac
