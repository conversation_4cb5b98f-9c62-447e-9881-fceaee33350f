#!/bin/bash
set -e

if [ -z "$1" ]; then
    echo "Usage: $0 <package-name>"
    echo "Example: $0 cobo-async-signals"
    exit 1
fi

PACKAGE_NAME=$1
PACKAGE_DIR="packages/$PACKAGE_NAME"

if [ ! -d "$PACKAGE_DIR" ]; then
    echo "Error: Package directory '$PACKAGE_DIR' not found"
    exit 1
fi

echo "Initializing Python environment for $PACKAGE_NAME..."
cd "$PACKAGE_DIR"

# Create virtual environment
echo "Creating virtual environment..."
uv venv .venv

# Install the package with dev dependencies
echo "Installing package with dev dependencies..."
unset VIRTUAL_ENV
uv sync --all-extras

# Install pre-commit hooks (if not already installed globally)
if [ -f "../../.pre-commit-config.yaml" ]; then
    echo "Installing pre-commit hooks..."
    uv run pre-commit install --config ../../.pre-commit-config.yaml
fi

echo "Environment for $PACKAGE_NAME initialized successfully!"
echo ""
echo "To activate the environment:"
echo "  cd $PACKAGE_DIR"
echo "  source .venv/bin/activate"
echo ""
echo "Or use the dev script:"
echo "  ./scripts/dev.sh $PACKAGE_NAME shell"
