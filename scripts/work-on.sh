#!/bin/bash
set -e

if [ -z "$1" ]; then
    echo "Usage: $0 <package-name> [command]"
    echo ""
    echo "This script helps you work on a specific package from the root directory."
    echo "It uses the top-level environment if available, otherwise suggests creating one."
    echo ""
    echo "Commands:"
    echo "  setup    - Initialize top-level environment for the package"
    echo "  test     - Run tests for the package"
    echo "  shell    - Open a shell in the top-level environment"
    echo "  python   - Open Python REPL with the package loaded"
    echo ""
    echo "Examples:"
    echo "  ./scripts/work-on.sh cobo-async-signals setup"
    echo "  ./scripts/work-on.sh cobo-async-signals test"
    echo "  ./scripts/work-on.sh cobo-async-signals python"
    exit 1
fi

PACKAGE_NAME=$1
COMMAND=${2:-shell}
PACKAGE_DIR="packages/$PACKAGE_NAME"

if [ ! -d "$PACKAGE_DIR" ]; then
    echo "Error: Package '$PACKAGE_NAME' not found in packages/"
    exit 1
fi

# Check if top-level environment exists
if [ ! -f ".venv/bin/activate" ]; then
    echo "No top-level environment found."
    echo "Run: ./scripts/work-on.sh $PACKAGE_NAME setup"
    exit 1
fi

case "$COMMAND" in
    setup)
        echo "Setting up top-level environment for $PACKAGE_NAME..."
        ./scripts/init-top-env.sh "$PACKAGE_NAME"
        ;;

    test)
        echo "Running tests for $PACKAGE_NAME..."
        uv run python -m pytest "$PACKAGE_DIR/tests" -v
        ;;

    shell)
        echo "Opening shell with $PACKAGE_NAME environment..."
        source .venv/bin/activate
        echo "Environment activated. Package $PACKAGE_NAME is available."
        echo "Try: python -c \"import ${PACKAGE_NAME//-/_}; print('Package loaded!')\""
        exec $SHELL
        ;;

    python)
        echo "Opening Python REPL with $PACKAGE_NAME loaded..."
        source .venv/bin/activate
        python -c "
import ${PACKAGE_NAME//-/_}
print('Package ${PACKAGE_NAME} loaded successfully!')
print('Available in the global namespace as: ${PACKAGE_NAME//-/_}')
print('Type exit() to quit')
"
        python
        ;;

    *)
        echo "Unknown command: $COMMAND"
        echo "Run '$0' without arguments to see usage"
        exit 1
        ;;
esac
