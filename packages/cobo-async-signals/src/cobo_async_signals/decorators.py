"""
Decorator utilities for signals
"""

from typing import Callable, Optional
from .signal import Signal


def signal_handler(
    signal: Signal, *, priority: int = 0, weak: bool = True
) -> Callable[[Callable], Callable]:
    """
    Decorator to connect a function as a signal handler.

    Usage:
        @signal_handler(my_signal, priority=10)
        def handle_signal(*args, **kwargs):
            ...
    """

    def decorator(func: Callable) -> Callable:
        signal.connect(func, priority=priority, weak=weak)
        return func

    return decorator
