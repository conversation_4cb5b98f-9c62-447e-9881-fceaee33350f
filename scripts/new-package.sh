#!/bin/bash
set -e

if [ -z "$1" ]; then
    echo "Usage: $0 <package-name>"
    echo "Example: $0 cobo-http-client"
    exit 1
fi

PACKAGE_NAME=$1
PACKAGE_DIR="packages/$PACKAGE_NAME"

if [ -d "$PACKAGE_DIR" ]; then
    echo "Error: Package '$PACKAGE_NAME' already exists"
    exit 1
fi

echo "Creating new package: $PACKAGE_NAME"

# Create package structure
mkdir -p "$PACKAGE_DIR"/{src,tests,docs}
mkdir -p "$PACKAGE_DIR/src/${PACKAGE_NAME//-/_}"

# Create pyproject.toml
cat > "$PACKAGE_DIR/pyproject.toml" << EOF
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "$PACKAGE_NAME"
version = "0.1.0"
description = "Package description here"
authors = [
    {name = "Cobo", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
keywords = []
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = []

# Package-specific dev dependencies (in addition to inherited dev deps)
# NOTE: Common dev dependencies are auto-synced from root pyproject.toml
[project.optional-dependencies]
dev = [
    # Inherit common dev dependencies from root
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    # Package-specific dev dependencies
    # Add any package-specific dev dependencies here
]

[project.urls]
Homepage = "https://github.com/cobo/cobo-libs-monorepo"
Repository = "https://github.com/cobo/cobo-libs-monorepo"
Issues = "https://github.com/cobo/cobo-libs-monorepo/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
asyncio_mode = "auto"

[tool.black]
line-length = 120
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 120

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
EOF

# Create README.md
cat > "$PACKAGE_DIR/README.md" << EOF
# $PACKAGE_NAME

Package description here.

## Installation

\`\`\`bash
pip install $PACKAGE_NAME
\`\`\`

## Usage

\`\`\`python
# Example usage here
\`\`\`

## License

MIT License - see LICENSE file in the repository root.
EOF

# Create CHANGELOG.md
cat > "$PACKAGE_DIR/CHANGELOG.md" << EOF
# Changelog

All notable changes to $PACKAGE_NAME will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - $(date +%Y-%m-%d)

### Added
- Initial release
EOF

# Create MANIFEST.in
cat > "$PACKAGE_DIR/MANIFEST.in" << EOF
include LICENSE
include README.md
include CHANGELOG.md
exclude tests/*
exclude docs/*
global-exclude __pycache__
global-exclude *.py[co]
EOF

# Create __init__.py
PACKAGE_MODULE="${PACKAGE_NAME//-/_}"
cat > "$PACKAGE_DIR/src/$PACKAGE_MODULE/__init__.py" << EOF
"""
$PACKAGE_NAME - Package description here
"""

__version__ = "0.1.0"
__all__ = []
EOF

# Create test file
cat > "$PACKAGE_DIR/tests/__init__.py" << EOF
# Test package for $PACKAGE_NAME
EOF

cat > "$PACKAGE_DIR/tests/test_basic.py" << EOF
"""
Basic tests for $PACKAGE_NAME
"""


def test_import():
    """Test that the package can be imported."""
    import $PACKAGE_MODULE
    assert hasattr($PACKAGE_MODULE, '__version__')
EOF

echo "Package $PACKAGE_NAME created successfully!"
echo ""
echo "Next steps:"
echo "1. Initialize the package environment:"
echo "   ./scripts/dev.sh $PACKAGE_NAME init"
echo "2. Update the package description in $PACKAGE_DIR/pyproject.toml"
echo "3. Add package-specific dependencies to pyproject.toml [project.dependencies]"
echo "4. Add dev dependencies to pyproject.toml [project.optional-dependencies.dev]"
echo "5. Run tests:"
echo "   ./scripts/dev.sh $PACKAGE_NAME test"