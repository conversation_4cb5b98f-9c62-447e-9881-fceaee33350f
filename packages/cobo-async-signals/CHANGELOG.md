# Changelog

All notable changes to cobo-async-signals will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2024-01-14

### Added
- Initial release
- Signal class with async/sync handler support
- Decorator-based signal connection
- Weak reference support for handlers
- Priority-based handler execution
- signal_handler decorator utility