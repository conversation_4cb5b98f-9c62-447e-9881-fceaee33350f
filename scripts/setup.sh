#!/bin/bash
set -e

echo "Setting up cobo-libs-monorepo development environment..."

# Ensure uv is installed
if ! command -v uv &> /dev/null; then
    echo "uv is not installed. Please install it first:"
    echo "curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Sync dev dependencies from root to all packages
echo "Syncing dev dependencies..."
./scripts/sync-dev-deps.sh

# Initialize all package environments
echo "Initializing package environments..."
./scripts/init-all-envs.sh

echo "Setup complete! Development environment is ready."
echo ""
echo "Common commands:"
echo "  ./scripts/dev.sh <package> test      - Run tests for a package"
echo "  ./scripts/dev.sh <package> shell     - Open shell in package environment"
echo "  ./scripts/test-all.sh                - Run all tests"
echo "  ./scripts/dev.sh <package> format    - Format package code"
echo "  ./scripts/dev.sh <package> lint      - Lint package code"
echo "  ./scripts/publish.sh <package>       - Publish a package to PyPI"