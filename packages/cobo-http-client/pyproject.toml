[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cobo-http-client"
version = "0.1.0"
description = "Async HTTP client with retry and circuit breaker support"
authors = [
    {name = "Cobo", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
keywords = ["http", "client", "async", "aiohttp", "retry", "circuit-breaker"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "aiohttp>=3.8.0",
    "tenacity>=8.0.0",
    "pydantic>=2.0.0",
]

# Package-specific dev dependencies (in addition to inherited dev deps)
# NOTE: Common dev dependencies are auto-synced from root pyproject.toml
[project.optional-dependencies]
dev = [
    # Inherit common dev dependencies from root
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "build>=0.10.0",
    "twine>=4.0.0",
    "ruff>=0.1.0",
    "pre-commit>=3.0.0",
    "aioresponses>=0.7.0",
    "pytest-httpserver>=1.0.0",
    # Package-specific dev dependencies
    # Add any package-specific dev dependencies here
]

[project.urls]
Homepage = "https://github.com/cobo/cobo-libs-monorepo"
Repository = "https://github.com/cobo/cobo-libs-monorepo"
Issues = "https://github.com/cobo/cobo-libs-monorepo/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
asyncio_mode = "auto"

[tool.black]
line-length = 120
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 120

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
