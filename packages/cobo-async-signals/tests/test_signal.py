"""
Tests for Signal class
"""

import asyncio
import pytest
from cobo_async_signals import Signal


class TestSignal:
    def test_sync_handler(self):
        """Test connecting and emitting with sync handlers."""
        signal = Signal()
        results = []

        @signal.connect
        def handler(value):
            results.append(value)
            return value * 2

        returned = signal.emit_sync(10)
        assert results == [10]
        assert returned == [20]

    @pytest.mark.asyncio
    async def test_async_handler(self):
        """Test connecting and emitting with async handlers."""
        signal = Signal()
        results = []

        @signal.connect
        async def handler(value):
            await asyncio.sleep(0.01)
            results.append(value)
            return value * 2

        returned = await signal.emit(10)
        assert results == [10]
        assert returned == [20]

    @pytest.mark.asyncio
    async def test_mixed_handlers(self):
        """Test mixing sync and async handlers."""
        signal = Signal()
        results = []

        @signal.connect
        def sync_handler(value):
            results.append(f"sync:{value}")

        @signal.connect
        async def async_handler(value):
            await asyncio.sleep(0.01)
            results.append(f"async:{value}")

        await signal.emit(5)
        assert sorted(results) == ["async:5", "sync:5"]

    def test_priority(self):
        """Test handler priority ordering."""
        signal = Signal()
        results = []

        @signal.connect(priority=1)
        def low_priority():
            results.append("low")

        @signal.connect(priority=10)
        def high_priority():
            results.append("high")

        @signal.connect(priority=5)
        def medium_priority():
            results.append("medium")

        signal.emit_sync()
        assert results == ["high", "medium", "low"]

    def test_disconnect(self):
        """Test disconnecting handlers."""
        signal = Signal()
        results = []

        def handler(value):
            results.append(value)

        signal.connect(handler)
        signal.emit_sync(1)
        assert results == [1]

        signal.disconnect(handler)
        signal.emit_sync(2)
        assert results == [1]  # No change

    def test_weak_references(self):
        """Test that weak references work correctly."""
        signal = Signal()
        results = []

        def create_handler():
            def handler(value):
                results.append(value)

            signal.connect(handler)
            return handler

        # Handler goes out of scope
        create_handler()

        # Force garbage collection
        import gc

        gc.collect()

        # Handler should be gone
        signal.emit_sync(1)
        assert results == []

    def test_strong_reference(self):
        """Test that strong references work when specified."""
        signal = Signal()
        results = []

        def create_handler():
            def handler(value):
                results.append(value)

            signal.connect(handler, weak=False)

        create_handler()

        # Force garbage collection
        import gc

        gc.collect()

        # Handler should still be there
        signal.emit_sync(1)
        assert results == [1]

    def test_exception_handling(self):
        """Test that exceptions in handlers don't break emission."""
        signal = Signal()
        results = []

        @signal.connect
        def failing_handler():
            raise ValueError("Test error")

        @signal.connect
        def working_handler():
            results.append("worked")

        signal.emit_sync()
        assert results == ["worked"]

    def test_kwargs_passing(self):
        """Test passing keyword arguments."""
        signal = Signal()
        results = []

        @signal.connect
        def handler(name, age=None):
            results.append({"name": name, "age": age})

        signal.emit_sync("Alice", age=30)
        assert results == [{"name": "Alice", "age": 30}]
