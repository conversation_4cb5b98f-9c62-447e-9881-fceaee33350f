# Top-Level Environment Management

This document explains how to set up and use a top-level Python environment to work on specific packages from the root directory.

## Overview

Instead of working in each package's individual environment, you can initialize a top-level environment that focuses on a specific package. This is useful when:

- You want to work on a single package primarily
- You prefer to run commands from the root directory
- You need to test interactions between packages
- You want easier access to debugging and development tools

## Setup

### Initialize Top-Level Environment

```bash
# Initialize top-level environment for a specific package
./scripts/dev.sh cobo-async-signals init-top

# Or use the dedicated script
./scripts/init-top-env.sh cobo-async-signals
```

This will:
1. Create a `.venv/` directory in the root
2. Install the specified package in editable mode with its dev dependencies
3. Install pre-commit hooks
4. Set up the environment for immediate use

### Directory Structure

After initialization:
```
cobo-libs-monorepo/
├── .venv/                  # Top-level environment
│   ├── bin/python
│   ├── lib/python3.10/
│   └── pyvenv.cfg
├── packages/
│   └── cobo-async-signals/
│       ├── .venv/          # Package-specific environment (still available)
│       ├── src/
│       └── tests/
└── scripts/
```

## Usage

### Using the work-on.sh Script

The `work-on.sh` script provides convenient commands for working with packages from the top level:

```bash
# Setup environment
./scripts/work-on.sh cobo-async-signals setup

# Run tests
./scripts/work-on.sh cobo-async-signals test

# Open shell with package available
./scripts/work-on.sh cobo-async-signals shell

# Open Python REPL with package loaded
./scripts/work-on.sh cobo-async-signals python
```

### Direct Commands

You can also use commands directly:

```bash
# Activate environment
source .venv/bin/activate

# Run tests
python -m pytest packages/cobo-async-signals/tests -v

# Import and use the package
python -c "import cobo_async_signals; print('Package loaded!')"

# Use uv run for one-off commands
uv run python -m pytest packages/cobo-async-signals/tests
```

## Workflows

### Single Package Development

When focusing on one package:

1. **Setup**: Initialize top-level environment
   ```bash
   ./scripts/work-on.sh cobo-async-signals setup
   ```

2. **Daily Development**: Work from root directory
   ```bash
   # Run tests
   ./scripts/work-on.sh cobo-async-signals test

   # Open development shell
   ./scripts/work-on.sh cobo-async-signals shell

   # Edit files with your preferred editor
   code packages/cobo-async-signals/src/
   ```

3. **Testing**: Test from root
   ```bash
   # Quick test
   uv run python -m pytest packages/cobo-async-signals/tests -v

   # Test with coverage
   uv run python -m pytest packages/cobo-async-signals/tests --cov=cobo_async_signals
   ```

### Multi-Package Development

When working on multiple packages:

1. **Keep individual environments**: Use package-specific environments
   ```bash
   ./scripts/dev.sh cobo-async-signals init
   ./scripts/dev.sh cobo-http-client init
   ```

2. **Use top-level for testing interactions**:
   ```bash
   # Install both packages in top-level environment
   ./scripts/init-top-env.sh cobo-async-signals
   uv pip install -e packages/cobo-http-client[dev]
   ```

### Switching Between Packages

To switch focus to a different package:

```bash
# Clean up current environment
rm -rf .venv

# Setup for new package
./scripts/work-on.sh cobo-http-client setup
```

## Advantages

### Top-Level Environment

✅ **Pros:**
- Work from root directory
- Easier command access
- Good for single package focus
- Simplified testing workflow
- Better IDE integration (single project root)

❌ **Cons:**
- Only one package focus at a time
- Potential dependency conflicts when adding multiple packages
- Need to recreate when switching packages

### Package-Specific Environments

✅ **Pros:**
- True isolation between packages
- Can work on multiple packages simultaneously
- No dependency conflicts
- Each package tests against its own dependencies

❌ **Cons:**
- Need to navigate to package directories
- More complex multi-package testing
- Separate environments to manage

## Best Practices

### 1. Choose the Right Approach

- **Top-level**: When primarily working on one package
- **Package-specific**: When working on multiple packages or need strict isolation

### 2. Environment Hygiene

```bash
# Clean up top-level environment when switching
rm -rf .venv

# Regularly update dependencies
./scripts/sync-dev-deps.sh
./scripts/work-on.sh <package> setup
```

### 3. Testing Strategy

```bash
# Test in top-level environment
./scripts/work-on.sh cobo-async-signals test

# Also test in package-specific environment for validation
./scripts/dev.sh cobo-async-signals test
```

### 4. IDE Configuration

For VS Code or similar IDEs, set the Python interpreter to:
- Top-level: `./venv/bin/python`
- Package-specific: `./packages/<package>/.venv/bin/python`

## Troubleshooting

### Environment Not Found

```bash
# Error: No top-level environment found
./scripts/work-on.sh cobo-async-signals setup
```

### Package Import Issues

```bash
# Check if package is installed
uv pip list | grep cobo-async-signals

# Reinstall if needed
uv pip install -e packages/cobo-async-signals[dev]
```

### Dependency Conflicts

```bash
# Check for conflicts
uv pip check

# Clean and recreate environment
rm -rf .venv
./scripts/init-top-env.sh cobo-async-signals
```

## Integration with Other Tools

### Pre-commit Hooks

Top-level environment includes pre-commit hooks:
```bash
# Hooks run automatically on commit
git commit -m "fix: update signal handling"

# Run manually
pre-commit run --all-files
```

### Testing

```bash
# Run tests with coverage
uv run python -m pytest packages/cobo-async-signals/tests --cov=cobo_async_signals --cov-report=html

# Run specific test
uv run python -m pytest packages/cobo-async-signals/tests/test_signal.py::TestSignal::test_async_handler -v
```

### Publishing

```bash
# Publish from top-level (package environment still needed)
./scripts/publish.sh cobo-async-signals
```

The top-level environment provides a convenient way to focus on single package development while maintaining the flexibility to use package-specific environments when needed.
