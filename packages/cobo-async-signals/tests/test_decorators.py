"""
Tests for decorator utilities
"""

import pytest
from cobo_async_signals import Signal, signal_handler


class TestDecorators:
    def test_signal_handler_decorator(self):
        """Test the signal_handler decorator."""
        signal = Signal()
        results = []

        @signal_handler(signal, priority=5)
        def handler(value):
            results.append(value)

        signal.emit_sync(10)
        assert results == [10]

    @pytest.mark.asyncio
    async def test_async_signal_handler_decorator(self):
        """Test the signal_handler decorator with async functions."""
        signal = Signal()
        results = []

        @signal_handler(signal)
        async def handler(value):
            results.append(value * 2)

        await signal.emit(5)
        assert results == [10]

    def test_multiple_decorators(self):
        """Test multiple handlers with decorators."""
        signal = Signal()
        results = []

        @signal_handler(signal, priority=10)
        def high_priority(value):
            results.append(f"high:{value}")

        @signal_handler(signal, priority=1)
        def low_priority(value):
            results.append(f"low:{value}")

        signal.emit_sync(5)
        assert results == ["high:5", "low:5"]
