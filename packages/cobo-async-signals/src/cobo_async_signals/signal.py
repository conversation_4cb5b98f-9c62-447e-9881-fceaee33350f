"""
Core Signal implementation
"""

import asyncio
import weakref
from typing import Any, Callable, List, Optional, Union
import inspect


class Signal:
    """
    Signal/event dispatcher supporting both sync and async handlers.
    """

    def __init__(self) -> None:
        self._handlers: List[tuple[Union[weakref.ref, Callable], int]] = []
        self._lock = asyncio.Lock()


    def connect(
        self,
        handler: Optional[Callable] = None,
        *,
        priority: int = 0,
        weak: bool = True,
    ) -> Union[Callable, Callable[[Callable], Callable]]:
        """
        Connect a handler to this signal.

        Can be used as a decorator:
            @signal.connect
            def handler(): ...

        Or directly:
            signal.connect(handler)
        """

        def decorator(func: Callable) -> Callable:
            if weak and hasattr(func, "__self__"):
                # For bound methods, store weak reference
                ref = weakref.WeakMethod(func, self._cleanup_handler)
                self._handlers.append((ref, priority))
            elif weak and not inspect.ismethod(func) and not inspect.isbuiltin(func):
                # For regular functions, store weak reference
                ref = weakref.ref(func, self._cleanup_handler)
                self._handlers.append((ref, priority))
            else:
                # Store strong reference
                self._handlers.append((func, priority))

            # Sort by priority (highest first)
            self._handlers.sort(key=lambda x: x[1], reverse=True)
            return func

        if handler is None:
            return decorator
        return decorator(handler)

    def disconnect(self, handler: Callable) -> None:
        """Disconnect a handler from this signal."""
        self._handlers = [
            (h, p) for h, p in self._handlers if not self._is_handler(h, handler)
        ]

    async def emit(self, *args: Any, **kwargs: Any) -> List[Any]:
        """
        Emit the signal, calling all connected handlers.

        Returns list of handler return values.
        """
        results = []

        async with self._lock:
            handlers = self._handlers.copy()

        for handler_ref, _ in handlers:
            handler = self._get_handler(handler_ref)
            if handler is None:
                continue

            try:
                if asyncio.iscoroutinefunction(handler):
                    result = await handler(*args, **kwargs)
                else:
                    result = handler(*args, **kwargs)
                results.append(result)
            except Exception as e:
                # Log error but continue with other handlers
                self.logger.error(f"Error in signal handler {handler}: {e}")

        return results

    def emit_sync(self, *args: Any, **kwargs: Any) -> List[Any]:
        """
        Synchronously emit the signal (async handlers will be skipped).
        """
        results = []

        for handler_ref, _ in self._handlers:
            handler = self._get_handler(handler_ref)
            if handler is None:
                continue

            if asyncio.iscoroutinefunction(handler):
                print(f"Warning: Skipping async handler {handler} in sync emit")
                continue

            try:
                result = handler(*args, **kwargs)
                results.append(result)
            except Exception as e:
                print(f"Error in signal handler {handler}: {e}")

        return results

    def _get_handler(
        self, handler_ref: Union[weakref.ref, Callable]
    ) -> Optional[Callable]:
        """Get the actual handler from a reference."""
        if isinstance(handler_ref, weakref.ref):
            return handler_ref()
        return handler_ref

    def _is_handler(
        self, handler_ref: Union[weakref.ref, Callable], target: Callable
    ) -> bool:
        """Check if handler_ref refers to the target handler."""
        handler = self._get_handler(handler_ref)
        return handler is target

    def _cleanup_handler(self, ref: weakref.ref) -> None:
        """Remove dead weak references."""
        # Remove dead weak references
        self._handlers = [(h, p) for h, p in self._handlers if h is not ref]

    def __len__(self) -> int:
        """Return the number of connected handlers."""
        # Count only live handlers
        return len([h for h, _ in self._handlers if self._get_handler(h) is not None])
