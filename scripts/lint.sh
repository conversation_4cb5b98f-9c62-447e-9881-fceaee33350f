#!/bin/bash
set -e

echo "Running code quality checks on all packages..."

for package_dir in packages/*/; do
    if [ -d "$package_dir" ]; then
        package_name=$(basename "$package_dir")
        echo "Running code quality checks for $package_name..."
        
        if [ ! -f "$package_dir/.venv/bin/activate" ]; then
            echo "Error: Environment for $package_name not initialized. Run: ./scripts/init-all-envs.sh"
            exit 1
        fi
        
        cd "$package_dir"
        source .venv/bin/activate
        
        # Format code
        echo "  Formatting with black..."
        black .
        
        # Sort imports
        echo "  Sorting imports with isort..."
        isort .
        
        # Lint with ruff
        echo "  Linting with ruff..."
        ruff check .
        
        # Type check with mypy
        echo "  Type checking with mypy..."
        mypy src
        
        cd - > /dev/null
        echo ""
    fi
done

echo "Code quality checks completed for all packages!"