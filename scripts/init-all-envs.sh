#!/bin/bash
set -e

echo "Initializing Python environments for all packages..."

# Find all packages
for package_dir in packages/*/; do
    if [ -d "$package_dir" ]; then
        package_name=$(basename "$package_dir")
        echo "Initializing environment for $package_name..."
        ./scripts/init-package-env.sh "$package_name"
        echo ""
    fi
done

echo "All package environments initialized successfully!"
echo ""
echo "Available commands:"
echo "  ./scripts/dev.sh <package> test    - Run tests for a package"
echo "  ./scripts/dev.sh <package> shell   - Open shell in package environment"
echo "  ./scripts/test-all.sh              - Run tests for all packages"