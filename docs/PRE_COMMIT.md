# Pre-commit Hooks

This project uses [pre-commit](https://pre-commit.com/) to maintain code quality and consistency across all packages.

## What are Pre-commit Hooks?

Pre-commit hooks are scripts that run automatically before each commit to:
- Format code consistently
- Check for common issues
- Run linters and type checkers
- Ensure code quality standards

## Setup

Pre-commit hooks are installed automatically when you run the setup script:

```bash
./scripts/setup.sh
```

Or install manually:

```bash
uv run pre-commit install
```

## Configured Hooks

### 1. General Hooks
- **trailing-whitespace**: Removes trailing whitespace
- **end-of-file-fixer**: Ensures files end with a newline
- **check-yaml**: Validates YAML files
- **check-toml**: Validates TOML files
- **check-merge-conflict**: Prevents committing merge conflict markers
- **check-added-large-files**: Prevents large files from being committed
- **check-docstring-first**: Ensures docstrings come first in Python files
- **debug-statements**: Prevents committing debug statements

### 2. Code Formatting
- **black**: Formats Python code consistently
- **isort**: Sorts and organizes imports

### 3. Code Quality
- **ruff**: Fast Python linter (replaces flake8 + others)
- **mypy**: Static type checking

### 4. Testing
- **pytest**: Runs tests on push (not on every commit)

## Usage

### Automatic Execution

Once installed, hooks run automatically on:
- `git commit` - Formatting and linting hooks
- `git push` - All hooks including tests

### Manual Execution

```bash
# Run all hooks on all files
uv run pre-commit run --all-files

# Run specific hook
uv run pre-commit run black

# Run hooks on specific files
uv run pre-commit run --files packages/cobo-async-signals/src/cobo_async_signals/signal.py
```

### Skipping Hooks

Sometimes you may need to skip hooks (use sparingly):

```bash
# Skip all hooks
git commit -m "message" --no-verify

# Skip specific hook
SKIP=mypy git commit -m "message"
```

## Common Workflows

### 1. Normal Development

```bash
# Make changes
vim packages/cobo-async-signals/src/cobo_async_signals/signal.py

# Commit (hooks run automatically)
git add -A
git commit -m "Add new signal feature"

# Push (tests run automatically)
git push
```

### 2. Fix Formatting Issues

If pre-commit fails due to formatting:

```bash
# Hooks will auto-fix many issues
git commit -m "message"

# If files were modified, stage and commit again
git add -A
git commit -m "message"
```

### 3. Run Quality Checks Manually

```bash
# Run the comprehensive lint script
./scripts/lint.sh

# Or run individual tools
uv run black packages/
uv run ruff check packages/
uv run mypy packages/
```

## Hook Configuration

The pre-commit configuration is in `.pre-commit-config.yaml`:

```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black
        language_version: python3.10
  # ... other hooks
```

### Updating Hook Versions

```bash
# Update to latest versions
uv run pre-commit autoupdate

# Install updated hooks
uv run pre-commit install
```

## Troubleshooting

### Hook Installation Issues

```bash
# Reinstall hooks
uv run pre-commit uninstall
uv run pre-commit install
```

### Hook Execution Problems

```bash
# Clear pre-commit cache
uv run pre-commit clean

# Run with verbose output
uv run pre-commit run --all-files --verbose
```

### Package-specific Issues

```bash
# Test specific package
cd packages/cobo-async-signals
uv run pre-commit run --all-files --config ../../.pre-commit-config.yaml
```

## Best Practices

1. **Always run setup script** when setting up a new environment
2. **Don't skip hooks** unless absolutely necessary
3. **Fix issues promptly** rather than accumulating them
4. **Update hooks regularly** to get latest improvements
5. **Run manual checks** before important commits or releases

## Integration with CI/CD

Pre-commit hooks complement CI/CD pipelines by:
- Catching issues early (before push)
- Reducing CI/CD failures
- Maintaining consistent code quality
- Speeding up review process

The same checks run in CI/CD should also run as pre-commit hooks to ensure consistency.