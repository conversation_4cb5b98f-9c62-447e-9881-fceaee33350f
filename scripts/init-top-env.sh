#!/bin/bash
set -e

if [ -z "$1" ]; then
    echo "Usage: $0 <package-name>"
    echo "Example: $0 cobo-async-signals"
    echo ""
    echo "This will initialize the package environment in the root directory"
    echo "allowing you to work on the package from the top level."
    exit 1
fi

PACKAGE_NAME=$1
PACKAGE_DIR="packages/$PACKAGE_NAME"

if [ ! -d "$PACKAGE_DIR" ]; then
    echo "Error: Package directory '$PACKAGE_DIR' not found"
    exit 1
fi

echo "Initializing top-level Python environment for $PACKAGE_NAME..."

# Create virtual environment in root
echo "Creating virtual environment in root directory..."
uv venv .venv

# Install the package with dev dependencies
echo "Installing $PACKAGE_NAME with dev dependencies..."
unset VIRTUAL_ENV
uv pip install -e "$PACKAGE_DIR[dev]"

# Install pre-commit hooks
if [ -f ".pre-commit-config.yaml" ]; then
    echo "Installing pre-commit hooks..."
    uv run pre-commit install
fi

echo "Top-level environment for $PACKAGE_NAME initialized successfully!"
echo ""
echo "To activate the environment:"
echo "  source .venv/bin/activate"
echo ""
echo "Or use commands directly:"
echo "  uv run python -m pytest $PACKAGE_DIR/tests"
echo "  uv run python -c \"import ${PACKAGE_NAME//-/_}; print('Package loaded successfully')\""
echo ""
echo "Note: This environment is focused on $PACKAGE_NAME but you can install other packages too."
