[project]
name = "cobo-libs-monorepo"
version = "0.0.0"
description = "Cobo libraries monorepo development environment"
requires-python = ">=3.10"

# Dev dependencies shared across all packages
[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "build>=0.10.0",
    "twine>=4.0.0",
    "ruff>=0.1.0",
    "pre-commit>=3.0.0",
]

# Note: This is not a workspace anymore. Each package has its own environment.
# This file only provides shared dev dependencies that packages can inherit from.

[tool.pytest.ini_options]
testpaths = ["tests", "packages/*/tests"]
python_files = ["test_*.py", "*_test.py"]
asyncio_mode = "auto"

[tool.black]
line-length = 120
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 120

[tool.ruff]
line-length = 120
target-version = "py310"

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true