# cobo-async-signals

Async-aware signal/event system for Python.

## Features

- Async and sync signal handlers
- Decorator-based signal connection
- Weak references to prevent memory leaks
- Thread-safe signal emission
- Priority-based handler execution

## Installation

```bash
pip install cobo-async-signals
```

## Quick Start

```python
from cobo_async_signals import Signal

# Create a signal
user_logged_in = Signal()

# Connect a sync handler
@user_logged_in.connect
def log_user_login(user_id, **kwargs):
    print(f"User {user_id} logged in")

# Connect an async handler
@user_logged_in.connect
async def send_welcome_email(user_id, **kwargs):
    await send_email(user_id, "Welcome!")

# Emit the signal
await user_logged_in.emit(user_id=123)
```

## License

MIT License - see LICENSE file in the repository root.