#!/bin/bash
set -e

echo "Running tests for all packages..."

for package_dir in packages/*/; do
    if [ -d "$package_dir" ]; then
        package_name=$(basename "$package_dir")
        echo "Testing $package_name..."
        
        if [ ! -f "$package_dir/.venv/bin/activate" ]; then
            echo "Error: Environment for $package_name not initialized. Run: ./scripts/init-all-envs.sh"
            exit 1
        fi
        
        cd "$package_dir"
        source .venv/bin/activate
        python -m pytest tests -v
        cd - > /dev/null
        echo ""
    fi
done

echo "All tests completed!"