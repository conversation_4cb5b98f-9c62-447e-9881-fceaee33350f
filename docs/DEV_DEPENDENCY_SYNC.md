# Development Dependency Synchronization

This document explains how to dynamically sync development dependencies from the root `pyproject.toml` to all packages.

## Overview

The monorepo uses a synchronization system that allows you to:
1. Define common dev dependencies once in the root `pyproject.toml`
2. Automatically sync these dependencies to all packages
3. Allow packages to add their own package-specific dev dependencies

## How It Works

### Root Configuration

The root `pyproject.toml` contains a template of common dev dependencies:

```toml
[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
```

### Package Configuration

Each package's `pyproject.toml` has a structure like:

```toml
# Package-specific dev dependencies (in addition to inherited dev deps)
# NOTE: Common dev dependencies are auto-synced from root pyproject.toml
[project.optional-dependencies]
dev = [
    # Inherit common dev dependencies from root
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    # Package-specific dev dependencies
    "aioresponses>=0.7.0",  # Example package-specific dependency
]
```

## Synchronization Commands

### Sync All Packages

```bash
# Sync dev dependencies from root to all packages
./scripts/sync-dev-deps.sh
```

This will:
1. Read dev dependencies from root `pyproject.toml`
2. Update each package's `pyproject.toml` with the common dependencies
3. Preserve any package-specific dependencies

### Sync Specific Package

```bash
# Sync dependencies for a specific package and reinstall
./scripts/dev.sh <package-name> sync
```

### Automatic Sync

Dev dependencies are automatically synced during:
- Initial setup: `./scripts/setup.sh`
- Package environment initialization

## Managing Dependencies

### Adding Common Dev Dependencies

1. Add the dependency to root `pyproject.toml`:
   ```toml
   [project.optional-dependencies]
   dev = [
       "pytest>=7.0.0",
       # ... other dependencies
       "new-tool>=1.0.0",  # Add new dependency here
   ]
   ```

2. Sync to all packages:
   ```bash
   ./scripts/sync-dev-deps.sh
   ```

3. Reinstall environments:
   ```bash
   ./scripts/init-all-envs.sh
   ```

### Adding Package-Specific Dev Dependencies

1. Add the dependency to the package's `pyproject.toml` in the "Package-specific dev dependencies" section:
   ```toml
   dev = [
       # ... common dependencies (auto-synced)
       # Package-specific dev dependencies
       "package-specific-tool>=1.0.0",  # Add here
   ]
   ```

2. Reinstall the package environment:
   ```bash
   ./scripts/dev.sh <package-name> install
   ```

### Removing Dependencies

For common dependencies:
1. Remove from root `pyproject.toml`
2. Sync to all packages: `./scripts/sync-dev-deps.sh`
3. Reinstall environments: `./scripts/init-all-envs.sh`

For package-specific dependencies:
1. Remove from the package's `pyproject.toml`
2. Reinstall the package: `./scripts/dev.sh <package-name> install`

## Sync Script Details

The sync script (`scripts/sync-dev-deps.py`) performs the following:

1. **Read Root Dependencies**: Parses the root `pyproject.toml` to extract dev dependencies
2. **Process Each Package**: For each package in `packages/`:
   - Reads the current `pyproject.toml`
   - Identifies package-specific dependencies (those not in root)
   - Updates the dev dependencies section with root dependencies + package-specific ones
   - Preserves the structure and comments
3. **Preserve Package-Specific Dependencies**: Ensures package-specific dependencies are not removed

## Best Practices

### 1. Version Consistency

Keep common dev tool versions consistent across all packages:
- Use the same version constraints in root `pyproject.toml`
- Sync regularly to ensure consistency

### 2. Package-Specific Dependencies

Only add package-specific dev dependencies when truly necessary:
- Testing frameworks specific to the package's domain
- Mock libraries for the package's specific dependencies
- Specialized tools for the package's use case

### 3. Regular Synchronization

Sync dependencies regularly:
- After adding new common dev tools
- When setting up new development environments
- As part of CI/CD processes

### 4. Version Updates

When updating common dev dependencies:
```bash
# 1. Update root pyproject.toml
# 2. Sync to all packages
./scripts/sync-dev-deps.sh
# 3. Test that all packages still work
./scripts/test-all.sh
# 4. Reinstall environments if needed
./scripts/init-all-envs.sh
```

## Troubleshooting

### Sync Script Fails

If the sync script fails:
1. Check that root `pyproject.toml` has valid TOML syntax
2. Ensure all package `pyproject.toml` files are valid
3. Check that the dev dependencies section exists in packages

### Dependencies Not Updated

If changes aren't reflected:
1. Check that the sync script completed successfully
2. Verify the package's `pyproject.toml` was updated
3. Reinstall the package environment: `./scripts/dev.sh <package> install`

### Package-Specific Dependencies Lost

If package-specific dependencies are removed:
1. The sync script preserves dependencies not in root
2. Check that the dependency was in the correct section
3. Re-add the dependency and run the sync script again

## Integration with CI/CD

Consider adding dependency sync checks to your CI/CD pipeline:

```bash
# Check if dependencies are in sync
./scripts/sync-dev-deps.sh --check  # (future enhancement)

# Or ensure sync is run
./scripts/sync-dev-deps.sh
git diff --exit-code  # Fails if there are changes
```

This ensures all packages stay synchronized with the root configuration.