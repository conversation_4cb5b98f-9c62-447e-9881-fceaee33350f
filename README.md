# Cobo Libraries Monorepo

This repository contains multiple Python packages developed by Cobo.

## Packages

- **cobo-async-signals**: Async-aware signal/event system for Python
- **cobo-http-client**: Async HTTP client with retry and circuit breaker support

## Development

This monorepo uses [uv](https://github.com/astral-sh/uv) for dependency management and workspace handling.

### Prerequisites

Install uv if you haven't already:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Setup

```bash
# Run the setup script to initialize all package environments
./scripts/setup.sh

# Or manually initialize all environments
./scripts/init-all-envs.sh

# Or initialize a specific package environment
./scripts/dev.sh <package-name> init
```

### Working with Packages

Each package has its own isolated Python environment but inherits common dev dependencies. This provides better isolation while avoiding dependency duplication.

```bash
# Run tests for all packages
./scripts/test-all.sh

# Run tests for a specific package
./scripts/dev.sh <package-name> test

# Open shell in package environment
./scripts/dev.sh <package-name> shell

# Format code for a package
./scripts/dev.sh <package-name> format

# Type check a package
./scripts/dev.sh <package-name> type

# Sync dev dependencies from root to all packages
./scripts/sync-dev-deps.sh

# Run all code quality checks on all packages
./scripts/lint.sh
```

### Publishing

Each package can be published independently to PyPI:

```bash
./scripts/publish.sh cobo-async-signals
```

## Project Structure

```
cobo-libs-monorepo/
├── .venv/                  # Optional: Top-level environment for single package focus
├── pyproject.toml          # Shared dev dependencies template
├── packages/
│   └── cobo-async-signals/
│       ├── .venv/          # Package-specific Python environment
│       ├── pyproject.toml  # Package config & dependencies
│       ├── src/
│       └── tests/
└── scripts/
    ├── setup.sh            # Initial setup script
    ├── init-all-envs.sh    # Initialize all package environments
    ├── init-top-env.sh     # Initialize top-level environment for a package
    ├── dev.sh              # Package development helper
    ├── work-on.sh          # Work on package from top-level
    ├── test-all.sh         # Run all tests
    └── publish.sh          # Publish packages to PyPI
```

## Environment Management

Each package can have its own dependencies while sharing common development tools. See [Environment Management Guide](docs/ENVIRONMENT_MANAGEMENT.md) for details.

## Top-Level Environment

You can initialize a package environment in the root directory for easier development. See [Top-Level Environment Guide](docs/TOP_LEVEL_ENVIRONMENT.md) for details.

## Development Dependency Synchronization

Common dev dependencies are automatically synced from the root to all packages. See [Dev Dependency Sync Guide](docs/DEV_DEPENDENCY_SYNC.md) for details.

## Code Quality

This project uses pre-commit hooks to maintain code quality. See [Pre-commit Guide](docs/PRE_COMMIT.md) for details.

### Quick Examples

```bash
# Create new package
./scripts/new-package.sh cobo-awesome-lib

# Initialize package environment (in package directory)
./scripts/dev.sh cobo-awesome-lib init

# OR initialize in top-level directory for easier access
./scripts/dev.sh cobo-awesome-lib init-top

# Add package-specific dependency (update pyproject.toml manually)
./scripts/dev.sh cobo-awesome-lib add requests

# Sync dev dependencies from root
./scripts/dev.sh cobo-awesome-lib sync

# Run package tests
./scripts/dev.sh cobo-awesome-lib test

# Work on a specific package (package environment)
./scripts/dev.sh cobo-awesome-lib shell

# Work on a specific package (top-level environment)
./scripts/work-on.sh cobo-awesome-lib shell
```

## License

See LICENSE file for details.
