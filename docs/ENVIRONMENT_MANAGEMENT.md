# Python Environment Management Strategy

This monorepo uses individual `uv` environments for each package, providing better isolation while sharing common development dependencies.

## Overview

```
┌─────────────────────────────────────────────┐
│          Root pyproject.toml                │
│  - Template for common dev dependencies     │
│  - Shared configuration                     │
└─────────────────────────────────────────────┘
                          │
                          │ Each package duplicates dev deps
                          │ but gets its own isolated environment
    ┌─────────────────────┴─────────────────────┬───────────────────────────┐
    │                                           │                           │
┌───▼──────────────┐                   ┌───────▼────────────┐      ┌───────▼────────────┐
│ cobo-async-signals│                   │ cobo-http-client   │      │ other-package      │
│ (.venv/)          │                   │ (.venv/)           │      │ (.venv/)           │
│                   │                   │                    │      │                    │
│ Dependencies:     │                   │ Dependencies:      │      │ Dependencies:      │
│ - (none)          │                   │ - aiohttp          │      │ - sqlalchemy       │
│                   │                   │ - tenacity         │      │ - alembic          │
│ Dev deps:         │                   │ - pydantic         │      │                    │
│ - pytest          │                   │                    │      │ Dev deps:          │
│ - black           │                   │ Dev deps:          │      │ - pytest           │
│ - mypy            │                   │ - pytest           │      │ - black            │
│ - ruff            │                   │ - black            │      │ - mypy             │
│ - ...             │                   │ - mypy             │      │ - ruff             │
│                   │                   │ - ruff             │      │ - factory-boy      │
│                   │                   │ - aioresponses     │      │ - ...              │
└───────────────────┘                   └────────────────────┘      └────────────────────┘
```

## How It Works

1. **Individual Environments**: Each package has its own `.venv/` directory with isolated dependencies
2. **Shared Configuration**: Common dev dependencies are duplicated in each package's `pyproject.toml`
3. **Better Isolation**: Packages can't interfere with each other's dependencies
4. **Independent Publishing**: Each package can be published separately with its own dependency versions

## Environment Management Scripts

### Initial Setup
```bash
# Setup all environments (includes dev dependency sync)
./scripts/setup.sh

# Or initialize environments manually
./scripts/init-all-envs.sh

# Initialize specific package (in package directory)
./scripts/dev.sh <package-name> init

# Initialize package environment in top-level directory
./scripts/dev.sh <package-name> init-top
```

### Dependency Synchronization
```bash
# Sync dev dependencies from root to all packages
./scripts/sync-dev-deps.sh

# Sync dependencies for specific package
./scripts/dev.sh <package-name> sync
```

### Working with Packages

```bash
# Run tests for a package
./scripts/dev.sh cobo-async-signals test

# Add a runtime dependency (temporary, update pyproject.toml manually)
./scripts/dev.sh cobo-http-client add httpx

# Open a Python shell in package environment
./scripts/dev.sh cobo-async-signals shell

# Format and lint a package
./scripts/dev.sh cobo-async-signals format
./scripts/dev.sh cobo-async-signals lint
```

### Working from Top-Level Directory

```bash
# Setup top-level environment for a specific package
./scripts/work-on.sh cobo-async-signals setup

# Run tests from top-level
./scripts/work-on.sh cobo-async-signals test

# Open shell with package available
./scripts/work-on.sh cobo-async-signals shell

# Open Python REPL with package loaded
./scripts/work-on.sh cobo-async-signals python
```

### Creating New Packages

```bash
# Create package structure
./scripts/new-package.sh cobo-new-feature

# Initialize its environment
./scripts/dev.sh cobo-new-feature init

# Add dependencies by editing pyproject.toml, then reinstall
./scripts/dev.sh cobo-new-feature install
```

## Environment Details

### What's in Each Environment

Each package environment contains:
- The package itself (installed in editable mode)
- All dependencies from `[project.dependencies]`
- All dev dependencies from `[project.optional-dependencies.dev]`
- Python tools (black, mypy, ruff, pytest, etc.)

### Package Structure

```
packages/cobo-async-signals/
├── .venv/                    # Package-specific virtual environment
│   ├── bin/python            # Python interpreter
│   ├── lib/python3.10/       # Installed packages
│   └── pyvenv.cfg            # Environment configuration
├── src/
│   └── cobo_async_signals/
├── tests/
└── pyproject.toml            # Package + dev dependencies
```

### Dependency Management

1. **Runtime Dependencies**: Add to `[project.dependencies]` in package's `pyproject.toml`
2. **Dev Dependencies**: Add to `[project.optional-dependencies.dev]`
3. **Shared Dev Tools**: Common tools are duplicated across packages for consistency

## Best Practices

### 1. Keep Environments Synchronized

When updating common dev dependencies:
```bash
# Update root pyproject.toml, then sync to all packages
./scripts/sync-dev-deps.sh

# Then reinstall environments
./scripts/init-all-envs.sh
```

### 2. Test in Isolation

Each package should be testable independently:
```bash
cd packages/cobo-async-signals
source .venv/bin/activate
python -m pytest tests/
```

### 3. Dependency Versions

- Use compatible version ranges: `aiohttp>=3.8.0,<4.0.0`
- Keep dev dependencies synchronized across packages
- Pin exact versions only when necessary

### 4. Environment Hygiene

```bash
# Clean and recreate environment
rm -rf packages/cobo-async-signals/.venv
./scripts/dev.sh cobo-async-signals init

# Check what's installed
./scripts/dev.sh cobo-async-signals shell
pip list
```

## Troubleshooting

### Environment Not Found

```bash
# Error: Package environment not initialized
./scripts/dev.sh cobo-async-signals init
```

### Dependency Issues

```bash
# Reinstall package with dependencies
./scripts/dev.sh cobo-async-signals install

# Or recreate environment
rm -rf packages/cobo-async-signals/.venv
./scripts/dev.sh cobo-async-signals init
```

### Pre-commit Issues

```bash
# Reinstall hooks in package environment
cd packages/cobo-async-signals
source .venv/bin/activate
pre-commit install --config ../../.pre-commit-config.yaml
```

## Advantages of This Approach

1. **True Isolation**: Packages can't interfere with each other
2. **Independent Versions**: Each package can use different dependency versions
3. **Easier Debugging**: Issues are isolated to specific packages
4. **Better Testing**: Each package tests against its own dependencies
5. **Flexible Publishing**: Packages can be published independently

## Trade-offs

1. **Disk Space**: Each environment duplicates dependencies
2. **Setup Time**: Initial setup takes longer
3. **Maintenance**: Need to keep dev dependencies synchronized manually
4. **Complexity**: More complex than a single shared environment

This approach prioritizes isolation and independence over resource efficiency, making it ideal for packages that may have different dependency requirements or release cycles.
