#!/usr/bin/env python3
"""
Sync dev dependencies from root pyproject.toml to all packages.
"""
import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Any

try:
    import tomllib
except ImportError:
    import tomli as tomllib

def read_toml(file_path: Path) -> Dict[str, Any]:
    """Read a TOML file."""
    with open(file_path, 'rb') as f:
        return tomllib.load(f)

def write_toml_section(file_path: Path, section_updates: Dict[str, Any]) -> None:
    """Update specific sections in a TOML file while preserving formatting."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    for section_key, new_content in section_updates.items():
        if section_key == 'dev_dependencies':
            # Update the dev dependencies section
            pattern = r'(# Package-specific dev dependencies.*?\ndev = \[)(.*?)(\])'
            
            # Build the new dev dependencies list
            dev_deps = []
            dev_deps.append('    # Inherit common dev dependencies from root')
            for dep in new_content:
                dev_deps.append(f'    "{dep}",')
            dev_deps.append('    # Package-specific dev dependencies')
            dev_deps.append('    # Add any package-specific dev dependencies here')
            
            new_dev_section = '\n'.join(dev_deps) + '\n'
            
            content = re.sub(pattern, r'\1\n' + new_dev_section + r'\3', content, flags=re.DOTALL)
    
    with open(file_path, 'w') as f:
        f.write(content)

def get_root_dev_dependencies() -> List[str]:
    """Get dev dependencies from root pyproject.toml."""
    root_toml = Path(__file__).parent.parent / 'pyproject.toml'
    
    if not root_toml.exists():
        print(f"Error: {root_toml} not found")
        sys.exit(1)
    
    root_config = read_toml(root_toml)
    
    try:
        return root_config['project']['optional-dependencies']['dev']
    except KeyError:
        print("Error: No dev dependencies found in root pyproject.toml")
        sys.exit(1)

def update_package_dev_dependencies(package_dir: Path, dev_deps: List[str]) -> None:
    """Update dev dependencies in a package's pyproject.toml."""
    pyproject_path = package_dir / 'pyproject.toml'
    
    if not pyproject_path.exists():
        print(f"Warning: {pyproject_path} not found, skipping")
        return
    
    print(f"Updating {package_dir.name}...")
    
    # Read current config to preserve package-specific deps
    current_config = read_toml(pyproject_path)
    current_dev_deps = current_config.get('project', {}).get('optional-dependencies', {}).get('dev', [])
    
    # Find package-specific dependencies (those not in root)
    package_specific_deps = []
    for dep in current_dev_deps:
        if dep not in dev_deps and not dep.startswith('#') and dep.strip():
            package_specific_deps.append(dep)
    
    # Combine root dev deps with package-specific deps
    all_dev_deps = dev_deps + package_specific_deps
    
    # Update the file
    write_toml_section(pyproject_path, {
        'dev_dependencies': all_dev_deps
    })
    
    print(f"  Updated with {len(dev_deps)} common + {len(package_specific_deps)} package-specific dependencies")

def main():
    """Main function to sync dev dependencies."""
    print("Syncing dev dependencies from root to all packages...")
    
    # Get root dev dependencies
    root_dev_deps = get_root_dev_dependencies()
    print(f"Found {len(root_dev_deps)} dev dependencies in root:")
    for dep in root_dev_deps:
        print(f"  - {dep}")
    print()
    
    # Find all packages
    packages_dir = Path(__file__).parent.parent / 'packages'
    if not packages_dir.exists():
        print("Error: packages directory not found")
        sys.exit(1)
    
    # Update each package
    updated_packages = []
    for package_dir in packages_dir.iterdir():
        if package_dir.is_dir() and (package_dir / 'pyproject.toml').exists():
            update_package_dev_dependencies(package_dir, root_dev_deps)
            updated_packages.append(package_dir.name)
    
    if updated_packages:
        print(f"\nSuccessfully updated {len(updated_packages)} packages:")
        for pkg in updated_packages:
            print(f"  - {pkg}")
        print("\nNext steps:")
        print("1. Review the changes in each package's pyproject.toml")
        print("2. Reinstall package environments: ./scripts/init-all-envs.sh")
    else:
        print("No packages found to update")

if __name__ == '__main__':
    main()